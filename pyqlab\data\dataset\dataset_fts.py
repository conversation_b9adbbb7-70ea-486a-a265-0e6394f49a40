from typing import Union, List, Tuple, Dict, Text, Optional
import pandas as pd
import numpy as np
from pprint import pprint
import torch
from inspect import getfullargspec
from torch.utils.data import Dataset
from pyqlab.data.dataset.handler import DataHandler

# device = "cuda" if torch.cuda.is_available() else "cpu"

# def _to_tensor(x):
#     if not isinstance(x, torch.Tensor):
#         return torch.tensor(x, dtype=torch.float, device=device)
#     return x

"""
Factor Time Series Dataset
提供两种模式：
1. 以MLP,CNN,TCN等模型的输入输出模式
2. 以Transformer,GPT等模型的输入输出模式
"""

class FTSDataset(Dataset):
    """
    (A)icm (H)istory (F)actor Dataset
    """
    def __init__(
        self,
        handler: Union[Dict, DataHandler],
        model_type=None,
        seq_len=20,
        label_len=0,
        pred_len=1,
        **kwargs
    ):
        super().__init__()
        self.model_type = model_type
        self.seq_len = seq_len
        self.label_len = label_len
        if self.model_type == 0:
            self.pred_len = 0
        else:
            self.pred_len = pred_len
        print(f"FTSDataset model_type: {self.model_type}, seq_len: {self.seq_len}, label_len: {self.label_len}, pred_len: {self.pred_len}")

        self.handler = handler

        self.datas = Tuple[np.ndarray, np.ndarray, np.ndarray] if self.model_type == 0 else Tuple[np.ndarray, np.ndarray, np.ndarray, List, List]

        # 新增数据访问属性，用于兼容原有的数据访问方式
        self.ft_df = pd.DataFrame()
        self.lb_df = pd.DataFrame()
        self.codecount = []

        # 新的数据结构属性
        self.code_data = None
        self.x_data = None
        self.y_data = None
        self.x_mark = None
        self.y_mark = None

    def config(self, handler_kwargs: dict = None, **kwargs):
        """配置handler参数，适配新的DataHandler接口"""
        if handler_kwargs is not None:
            # 直接更新DataHandler的配置对象，避免调用复杂的config方法
            for key, value in handler_kwargs.items():
                if hasattr(self.handler.config, key):
                    setattr(self.handler.config, key, value)
                    print(f"更新配置: {key} = {value}")

    def setup_data(self, handler_kwargs: dict = None, **kwargs):
        """设置数据，适配新的DataHandler接口"""
        if handler_kwargs is not None:
            self.handler.config(**handler_kwargs)

        # 调用新的DataHandler的setup_data方法
        self.handler.setup_data()

        # 设置兼容性数据访问属性
        self._setup_compatibility_data_access()

        self.datas = self.prepare(**kwargs)


    def _setup_compatibility_data_access(self):
        """设置兼容性数据访问属性，使原有的__getitem__方法能正常工作"""
        # 获取主要特征数据
        main_fd_name = self.handler.config.main_fd_name
        if main_fd_name in self.handler.fd_dfs:
            self.ft_df = self.handler.fd_dfs[main_fd_name]

        # 获取标签数据
        self.lb_df = self.handler.lb_df

        # 计算codecount - 每个代码的累计数量
        if not self.lb_df.empty:
            code_counts = self.lb_df.groupby('code_encoded').size().values
            self.codecount = np.cumsum(code_counts).tolist()
        else:
            self.codecount = []

    def get_ins_nums(self):
        return self.handler._get_ins_nums()

    def save_model_inputs_config(self, save_path: str):
        self.handler._dump_input_param_json(
            save_path,
            self.model_type,
            self.seq_len,
            self.label_len,
            self.pred_len
            )

    def prepare(
        self,
        **kwargs
    ) -> Union[List[pd.DataFrame], pd.DataFrame]:
        """
        dataset using must call this function to call->fetch() load data
        适配新的DataHandler.fetch接口
        """
        print(f"FTSDataset parameter: {kwargs}")

        # 新的DataHandler.fetch需要三个参数：direct, win, filter_win
        # 从kwargs中提取这些参数，如果没有则使用默认值
        direct = kwargs.get('direct', 'ls')  # 默认long-short
        win = kwargs.get('win', self.seq_len)  # 使用seq_len作为默认窗口大小
        filter_win = kwargs.get('filter_win', 0)  # 默认不过滤

        # 获取采样后的数据
        fetch_result = self.handler.fetch(direct, win, filter_win)

        # 存储采样后的数据供__getitem__使用
        if len(fetch_result) == 3:
            # 没有时间特征
            self.code_data, self.x_data, self.y_data = fetch_result
            self.x_mark, self.y_mark = None, None
        else:
            # 有时间特征
            self.code_data, self.x_data, self.y_data, self.x_mark, self.y_mark = fetch_result

        print(f"采样后数据形状: code_data={self.code_data.shape}, x_data={self.x_data.shape}, y_data={self.y_data.shape}")

        return fetch_result
    
    def __len__(self):
        """返回数据集长度"""
        if hasattr(self, 'y_data') and self.y_data is not None:
            return len(self.y_data)
        return 0

    def __getitem__(self, i):
        """
        获取数据项，适配新的数据结构
        新的DataHandler.fetch已经返回采样好的数据，直接使用索引访问
        """
        if not hasattr(self, 'x_data') or self.x_data is None:
            raise RuntimeError("数据未准备好，请先调用setup_data()和prepare()方法")

        if self.model_type == 0:
            # MLP/CNN/TCN模式：返回(code, x, y)
            code = torch.tensor(self.code_data[i], dtype=torch.int32)

            # x_data的形状是(样本数, 期数, 窗口大小, 特征数)，需要合并期数和特征数
            # 对于model_type=0，通常只使用主要期数的数据
            if len(self.x_data.shape) == 4:
                # 取第一个期数的数据，形状变为(窗口大小, 特征数)
                x = torch.tensor(self.x_data[i, 0], dtype=torch.float32)
            else:
                # 如果是3维，直接使用
                x = torch.tensor(self.x_data[i], dtype=torch.float32)

            y = torch.tensor(self.y_data[i], dtype=torch.float32)
            return code, x, y

        elif self.model_type == 1:
            # Transformer/GPT模式：返回(code, x_data, x_mark, y_data, y_mark)
            code = torch.tensor(self.code_data[i], dtype=torch.int32)

            # 对于Transformer模式，需要处理序列数据
            if len(self.x_data.shape) == 4:
                # 合并多期数据：(期数, 窗口大小, 特征数) -> (窗口大小, 总特征数)
                x_data = self.x_data[i].reshape(self.x_data.shape[2], -1)
            else:
                x_data = self.x_data[i]
            x_data = torch.tensor(x_data, dtype=torch.float32)

            # 对于预测任务，y_data通常是标量，需要扩展为序列
            if self.pred_len > 0:
                # 创建预测长度的序列，这里简单重复标签值
                y_data = torch.tensor([self.y_data[i]] * self.pred_len, dtype=torch.float32)
            else:
                y_data = torch.tensor(self.y_data[i], dtype=torch.float32)

            # 时间特征
            if self.x_mark is not None and self.y_mark is not None:
                x_mark = torch.tensor(self.x_mark[i], dtype=torch.float32)
                y_mark = torch.tensor(self.y_mark[i], dtype=torch.float32)
                # 如果y_mark是标量，扩展为序列
                if len(y_mark.shape) == 1 and self.pred_len > 0:
                    y_mark = y_mark.unsqueeze(0).repeat(self.pred_len, 1)
            else:
                # 如果没有时间特征，创建零填充的时间特征
                x_mark = torch.zeros(self.seq_len, 5, dtype=torch.float32)
                y_mark = torch.zeros(self.pred_len, 5, dtype=torch.float32)

            return code, x_data, x_mark, y_data, y_mark
        else:
            raise ValueError("model_type must be 0 or 1")
    
